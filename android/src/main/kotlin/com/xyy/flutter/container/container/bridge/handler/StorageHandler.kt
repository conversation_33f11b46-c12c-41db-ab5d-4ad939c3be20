package com.xyy.flutter.container.container.bridge.handler

import android.content.Context
import android.content.SharedPreferences
import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.flutter.container.container.bridge.ContainerErrorCode
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity
import org.json.JSONArray
import org.json.JSONObject
import java.lang.Exception

class StorageHandler : BaseHandler() {

    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        val space = params["space"]?.toString() ?: "default"
        val key = params["key"]?.toString() ?: ""
        val type = params["type"]?.toString() ?: ""
        val valueObj = params["data"]


        val sp = activity.getSharedPreferences(space, Context.MODE_PRIVATE)

        when (type) {
            "putAll" -> {
                putAll(sp, valueObj)
            }
            "getAll" -> {
                getAll(sp)
            }
            "getValue" -> {
                getValue(sp, key)
            }
            "deleteAll" -> {
                deleteAll(sp)
            }
        }

    }

    private fun deleteAll(sp: SharedPreferences?) {
        val commit = sp?.edit()?.clear()?.commit()
        result?.success(commit)
    }


    private fun getAll(sp: SharedPreferences) {
        result?.success(sp.all)
    }

    private fun getValue(sp: SharedPreferences, key: String) {
        val value = sp.all[key]
        if (value is String) {
            try {
                val jsonArray = JSONArray(value)
                val arrayList = arrayListOf<Any?>()
                for (i in 0 until jsonArray.length()) {
                    arrayList.add(jsonArray.get(i))
                }
                result?.success(arrayList)
                return
            } catch (ignore: Exception) {
            }
            try {
                val jsonObject = JSONObject(value)
                val hashMap = hashMapOf<String, Any?>()
                jsonObject.keys().forEach {
                    hashMap.put(it, jsonObject.get(it))
                }
                result?.success(hashMap)
                return
            } catch (ignore: Exception) {
            }

        }
        result?.success(value)
    }

    private fun putAll(sp: SharedPreferences, map: Any?) {
        if (map !is Map<*, *>) {
            error(ContainerErrorCode.VALUE_ERROR.errorCode, ContainerErrorCode.VALUE_ERROR.errorMsg)
            return
        }
        val edit = sp.edit()

        map.forEach {
            if (it.key is String) {
                val key = it.key as String
                when (val valueObj = it.value) {
                    null -> {
                        edit.remove(key)
                    }
                    is Boolean -> {
                        edit.putBoolean(key, valueObj)
                    }
                    is Int -> {
                        edit.putInt(key, valueObj)
                    }
                    is Long -> {
                        edit.putLong(key, valueObj)
                    }
                    is Double -> {
                        edit.putFloat(key, valueObj as Float)
                    }
                    is String -> {
                        edit.putString(key, valueObj)
                    }
                    is List<*> -> {
                        edit.putString(key, JSONArray(valueObj).toString())
                    }
                    is Map<*, *> -> {
                        edit.putString(key, JSONObject(valueObj).toString())
                    }
                }

            } else {
                error(ContainerErrorCode.KEY_ERROR.errorCode, ContainerErrorCode.KEY_ERROR.errorMsg)
                return
            }
        }
        val commit = edit.commit()
        result?.success(commit)
    }
}