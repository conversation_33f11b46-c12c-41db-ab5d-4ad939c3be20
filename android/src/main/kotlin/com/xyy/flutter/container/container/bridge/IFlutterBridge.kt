package com.xyy.flutter.container.container.bridge

import io.flutter.embedding.engine.FlutterEngine

interface IFlutterBridge {
    fun registerBridgeHandler(name: String?, handler: Class<out BaseHandler>?)

    fun removeBridgeHandler(name: String?)

    fun setBridgeImpl(impl: IBridgeImpl)

    fun getBridgeImpl(): IBridgeImpl?

    fun bridgeCall(name: String?, params: Any?, engine: FlutterEngine?)
}
