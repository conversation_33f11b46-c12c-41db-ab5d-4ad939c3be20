package com.xyy.flutter.container.container.bridge.photo;

import android.content.Context;
import android.graphics.BitmapFactory;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.recyclerview.widget.RecyclerView;

import com.xyy.flutter.container.container.R;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ybm on 2017/8/2.
 */

public class AlbumAdapter extends RecyclerView.Adapter<AlbumAdapter.PhotoHolder>
        implements View.OnClickListener {

    private OnItemClickListener mListener;
    private Context mContext;
    private LayoutInflater mInflater;
    private List<PhotoBean> mList;
    private SparseArray<PhotoBean> mSelected;
    private boolean mUseCamera;
    private int mMaxPiece;

    public AlbumAdapter(Context context) {
        mContext = context;
        mInflater = LayoutInflater.from(context);
        mList = new ArrayList<>();
        mSelected = new SparseArray<>();
        mUseCamera = true;
        mMaxPiece = 1;
    }

    public AlbumAdapter(Context context, int max, boolean camera) {
        this(context);
        mMaxPiece = max;
        mUseCamera = camera;
    }


    public void setData(List<PhotoBean> list) {
        mSelected.clear();
        mList.clear();
        if (list != null) {
            mList.addAll(list);
        }
        notifyDataSetChanged();
    }

    public ArrayList<PhotoBean> getSelected() {
        if (mSelected.size() <= 0) {
            return null;
        }
        ArrayList<PhotoBean> data = new ArrayList<>();
        for (int i = 0; i < mSelected.size(); i++) {
            data.add(mSelected.valueAt(i));
        }
        return data;
    }

    public void setOnClickListener(OnItemClickListener listener) {
        mListener = listener;
    }

    @Override
    public PhotoHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = mInflater.inflate(R.layout.flutter_item_photo, parent, false);
        return new PhotoHolder(view);
    }

    @Override
    public void onBindViewHolder(PhotoHolder holder, int position) {
        PhotoBean bean = mList.get(position);
        if (bean != null) {
            PhotoBean value = mSelected.get(position);
            holder.marker.setVisibility(value == null ? View.GONE : View.VISIBLE);
            holder.ivSelect.setImageResource(value == null ?
                    R.drawable.checkbox_normal : R.drawable.checkbox_check);
//            Glide.with(mContext).load(bean.path).into(holder.ivPhoto);
            holder.ivPhoto.setImageBitmap(BitmapFactory.decodeFile(bean.path));

            holder.root.setTag(holder);
            holder.root.setOnClickListener(null);
            holder.root.setOnClickListener(this);

        }
    }

    @Override
    public int getItemCount() {
        return mUseCamera ? mList.size() + 1 : mList.size();
    }


    @Override
    public void onClick(View v) {
        RecyclerView.ViewHolder holder = (RecyclerView.ViewHolder) v.getTag();
        int position = holder.getAdapterPosition();
        PhotoHolder photoHolder = (PhotoHolder) v.getTag();
        if (photoHolder == null || mList == null || position >= mList.size()) {
            return;
        }
        PhotoBean value = mSelected.get(position);
        if (mMaxPiece <= mSelected.size()) {
            if (value != null) {
                photoHolder.marker.setVisibility(View.GONE);
                photoHolder.ivSelect.setImageResource(R.drawable.checkbox_normal);
                mSelected.remove(position);
            } else {
                Toast.makeText(v.getContext(), "最多只能选" + mMaxPiece + "张", Toast.LENGTH_SHORT).show();
            }
        } else {
            //之前的保存的有，本次就去掉；反之保存的没有，就加上
            photoHolder.marker.setVisibility(value == null ? View.VISIBLE : View.GONE);
            photoHolder.ivSelect.setImageResource(value == null ?
                    R.drawable.checkbox_check : R.drawable.checkbox_normal);
            if (value == null) {
                mSelected.put(position, mList.get(position));
            } else {
                mSelected.remove(position);
            }
        }
        mListener.onItemClick(position, mSelected.size());
    }

    class PhotoHolder extends RecyclerView.ViewHolder {

        ImageView ivPhoto;
        ImageView ivSelect;
        View root;
        View marker;

        PhotoHolder(View itemView) {
            super(itemView);
            ivPhoto = (ImageView) itemView.findViewById(R.id.iv_photo);
            ivSelect = (ImageView) itemView.findViewById(R.id.iv_select);
            root = itemView.findViewById(R.id.root);
            marker = itemView.findViewById(R.id.view_marker);
        }
    }

    public interface OnItemClickListener {
        void onItemClick(int position, int count);
    }
}
