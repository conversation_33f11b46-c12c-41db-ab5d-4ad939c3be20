package com.xyy.flutter.container.container.route

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.net.Uri
import android.util.Log
import android.view.View
import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.ContainerRuntime
import java.lang.Exception

class CheckNativeInterceptor : IRouterInterceptor {
    override fun intercept(context: Context?, url: String?): String? {
        try {
            Log.e("guan", "intercept:${url}")
            val parse = Uri.parse(url)
            if (!parse.isRelative && parse.path?.startsWith("/flutter") != true) {
                context?.let {
                    Intent(Intent.ACTION_VIEW, parse).let { intent ->
                        val activity = getActivity(it)
                        if (activity != null) {
                            activity.startActivityForResult(intent, 10241)
                        } else {
                            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                            context.startActivity(intent)
                        }
                    }
                }
                return null
            }
            if (parse.path?.startsWith("/flutter") == true) {
                var newUrl = url?.removePrefix("${parse.scheme}://${parse.host}/flutter")
                return super.intercept(context, newUrl)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return super.intercept(context, url)
    }

    private fun getActivity(context: Context): Activity? {
        var tempContext = context
        while (tempContext is ContextWrapper) {
            if (tempContext is Activity) {
                return tempContext
            }
            tempContext = tempContext.baseContext
        }
        return null
    }
}
