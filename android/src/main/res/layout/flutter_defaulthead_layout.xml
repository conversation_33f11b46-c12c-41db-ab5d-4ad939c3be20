<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/default_unique_title_bar_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white"
    android:fitsSystemWindows="true">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/default_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?android:attr/actionBarSize"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/nav_return">

        <TextView
            android:id="@+id/tv_toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="1"
            android:textColor="#333333"
            android:textSize="17sp"
            tools:text="标题标题" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:layout_marginEnd="15dp"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_rightText"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:lines="1"
                android:maxEms="8"
                android:textColor="#333333"
                android:textSize="15sp"
                tools:text="成熟成熟" />

            <ImageView
                android:id="@+id/iv_rightBt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>

    </androidx.appcompat.widget.Toolbar>

    <View
        android:id="@+id/default_unique_title_bar_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@id/default_toolbar"
        android:background="#FFE4E5EA"
        app:layout_constraintTop_toBottomOf="@id/default_toolbar" />

</androidx.constraintlayout.widget.ConstraintLayout>