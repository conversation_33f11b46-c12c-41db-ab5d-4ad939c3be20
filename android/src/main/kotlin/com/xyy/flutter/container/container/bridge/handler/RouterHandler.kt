package com.xyy.flutter.container.container.bridge.handler

import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity

class RouterHandler : BaseHandler() {
    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        ContainerRuntime.getFlutterRouter().open(activity, params["url"] as String?)
    }
}