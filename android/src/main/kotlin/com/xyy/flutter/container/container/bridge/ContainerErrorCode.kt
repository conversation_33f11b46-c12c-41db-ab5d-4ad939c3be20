package com.xyy.flutter.container.container.bridge

enum class ContainerErrorCode(val errorCode: String, val errorMsg: String) {
    ACTIVITY_IS_NULL("-1000", "activity is null"),
    INTERNAL_ERROR("-1001", "An exception occurred in handling"),
    VALUE_ERROR("-1002", "value type is error"),
    KEY_ERROR("-1003", "key type is error"),
    NOT_IMPLEMENTED("-1004", "notImplemented"),

    NO_CAMERA_PERMISSION("-1011", "没有相机权限"),
    NO_STORAGE_PERMISSION("-1012", "没有存储权限"),
    NO_RESULT("-1013", "没有返回结果"),
    NO_EXIST("-1014", "文件不存在"),
    FILE_CORRUPTED("-1015", "文件已损坏"),
    RESULT_IS_EMPTY("-1016", "选择返回结果为空"),
}