package com.xyy.flutter.container.container.bridge

import com.xyy.flutter.container.container.ContainerConfig
import io.flutter.embedding.engine.FlutterEngine

class FlutterBridge(flutterBridge: IFlutterBridge) : IFlutterBridge by flutterBridge {


    class FlutterBridgeDelegate() : IFlutterBridge {
        var impl: IBridgeImpl? = null

        override fun registerBridgeHandler(name: String?, handler: Class<out BaseHandler>?) {
            if (!name.isNullOrEmpty()) {
                MethodCallDispatcher.registerCustomHandler(name, handler)
            }
        }

        override fun removeBridgeHandler(name: String?) {
            if (!name.isNullOrEmpty()) {
                MethodCallDispatcher.removeCustomHandler(name)
            }
        }

        override fun setBridgeImpl(impl: IBridgeImpl) {
            this.impl = impl
        }

        override fun getBridgeImpl(): IBridgeImpl? {
            return impl
        }

        override fun bridgeCall(name: String?, params: Any?, engine: FlutterEngine?) {
            if (!name.isNullOrEmpty()) {
                MethodCallDispatcher.bridgeCall(name, params, engine)
            }
        }

    }
}