package com.xyy.flutter.container.container.bridge.handler

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.tbruyelle.rxpermissions2.RxPermissions
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.flutter.container.container.bridge.ContainerErrorCode
import com.xyy.flutter.container.container.bridge.photo.AlbumActivity
import com.xyy.flutter.container.container.bridge.photo.CameraUtils
import com.xyy.flutter.container.container.bridge.photo.PhotoBean
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity
import io.reactivex.disposables.Disposable
import java.io.File
import java.util.*

class PhotoHandler : BaseHandler() {
    companion object {
        private const val FRAGMENT_TAG = "photo_handler_fragment"
    }

    private var permissionDispose: Disposable? = null


    @SuppressLint("CheckResult")
    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {

        //添加权限 打开相机
        permissionDispose = RxPermissions(activity).requestEach(
                Manifest.permission.CAMERA,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
        ).subscribe { permission ->
            if (Manifest.permission.CAMERA == permission.name) {
                if (!permission.granted) {
                    error(
                            ContainerErrorCode.NO_CAMERA_PERMISSION.errorCode,
                            ContainerErrorCode.NO_CAMERA_PERMISSION.errorMsg
                    )
                    permissionDispose?.dispose()
                }
            } else {
                if (!permission.granted) {
                    error(
                            ContainerErrorCode.NO_STORAGE_PERMISSION.errorCode,
                            ContainerErrorCode.NO_STORAGE_PERMISSION.errorMsg
                    )
                } else {
                    //打开相机或者相册
                    val emptyFragment = EmptyFragment()
                    emptyFragment.setParams(this, params)
                    activity.supportFragmentManager.beginTransaction()
                            .add(emptyFragment, FRAGMENT_TAG).commitAllowingStateLoss()
                }
            }
        }
    }

    class EmptyFragment : Fragment() {

        private var isFirstSelectImg = true

        private lateinit var handler: PhotoHandler

        private lateinit var photoParams: Map<String, Any?>

        fun setParams(handler: PhotoHandler, params: Map<String, Any?>) {
            photoParams = params
            this.handler = handler
        }

        override fun onCreate(savedInstanceState: Bundle?) {
            super.onCreate(savedInstanceState)
            val type = photoParams["type"]?.toString() ?: ""
            val count = photoParams["imageCount"]?.toString() ?: ""
            activity?.let {
                when (type) {
                    "Camera" -> {
                        openCamera(it)
                    }
                    "Album" -> {
                        openAlbum(count.toIntOrNull() ?: 1, it)
                    }

                }
            }
        }

        private fun openAlbum(count: Int, activity: FragmentActivity) {
            val intent = Intent(activity, AlbumActivity::class.java)
            //设置最大选择数量
            intent.putExtra(AlbumActivity.MAX_PIECE, count)
            startActivityForResult(intent, CameraUtils.REQUEST_GALLERY)
        }

        fun getCompressTempImgFile(): File? {
            val path = Environment.getExternalStorageDirectory().path + "/xyy/flutter/compress_temp_img"
            val file = File(path)
            if (!file.exists()) {
                file.mkdirs()
            }
            return file
        }

        /**
         * 清除压缩图片文件夹中的所有图片
         */
        fun clearCompressTempImg() {
            val parent = getCompressTempImgFile()
            if (parent != null) {
                val imgs = parent.listFiles()
                if (imgs != null && imgs.size != 0) {
                    for (i in imgs.indices) {
                        imgs[i].delete()
                    }
                }
            }
        }

        private fun openCamera(activity: FragmentActivity) {
            if (isFirstSelectImg) {
                getCompressTempImgFile()
                clearCompressTempImg()
                isFirstSelectImg = false
            }
            CameraUtils.openCamera(this)
        }

        private fun buildResult(fileList: List<String>) {
            handler.result?.success(fileList)
            parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
        }

        override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
            super.onActivityResult(requestCode, resultCode, data)
            if (resultCode == Activity.RESULT_OK) {
                when (requestCode) {
                    CameraUtils.REQUEST_GALLERY -> {
                        handleGalleryResult(data)
                    }
                    CameraUtils.REQUEST_CAMERA -> {
                        handleCameraResult()
                    }
                }
            } else {
                handler.error(ContainerErrorCode.NO_RESULT.errorCode, ContainerErrorCode.NO_RESULT.errorMsg)
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
            }
        }

        private fun handleCameraResult() {
            val sp = context!!.getSharedPreferences("flutter_sp", Context.MODE_PRIVATE)
            val fileName = sp.getString("imgPath", "");
            val file = File(fileName ?: "")
            if (!file.exists()) {
                handler.error(ContainerErrorCode.NO_EXIST.errorCode, ContainerErrorCode.NO_EXIST.errorMsg)
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                return
            }
            if (file.length() <= 0) {
                handler.error(ContainerErrorCode.FILE_CORRUPTED.errorCode, ContainerErrorCode.FILE_CORRUPTED.errorMsg)
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                return
            }
            buildResult(listOf(file.absolutePath))
        }

        private fun handleGalleryResult(data: Intent?) {
            val result: List<PhotoBean?>? =
                    data?.getParcelableArrayListExtra(AlbumActivity.EXTRA_RESULT)
            if (result.isNullOrEmpty()) {
                handler.error(
                        ContainerErrorCode.RESULT_IS_EMPTY.errorCode,
                        ContainerErrorCode.RESULT_IS_EMPTY.errorMsg
                )
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                return
            }
            val resultList = ArrayList<String>()

            result.forEach {
                if (it == null) {
                    handler.error(ContainerErrorCode.NO_EXIST.errorCode, ContainerErrorCode.NO_EXIST.errorMsg)
                    parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                    return
                }
                val urlFile = File(it.path)
                val uri = Uri.fromFile(urlFile)
                val file = CameraUtils.handleImageOn19(uri, activity)
                if (file == null || !file.exists()) {
                    handler.error(ContainerErrorCode.NO_EXIST.errorCode, ContainerErrorCode.NO_EXIST.errorMsg)
                    parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                    return
                }
                if (file.length() <= 0) {
                    handler.error(
                            ContainerErrorCode.FILE_CORRUPTED.errorCode,
                            ContainerErrorCode.FILE_CORRUPTED.errorMsg
                    )
                    parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                    return
                }
                resultList.add(file.absolutePath)
//                val sourceType = data.getIntExtra(AlbumActivity.SOURCE_TYPE, 0)
            }
            buildResult(resultList)
        }
    }


}