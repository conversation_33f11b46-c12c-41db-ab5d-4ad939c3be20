package com.xyy.flutter.container.container.bridge.handler

import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.flutter.container.container.bridge.ContainerErrorCode
import com.xyy.flutter.container.container.bridge.callback.UploadCallback
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity

class ReportErrorHandler : BaseHandler() {
    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        val errorDetail = params["errorDetail"]?.toString() ?: "--"

        val bridgeImpl = ContainerRuntime.bridge.getBridgeImpl()

        if (bridgeImpl == null) {
            error(ContainerErrorCode.NOT_IMPLEMENTED.errorCode, ContainerErrorCode.NOT_IMPLEMENTED.errorMsg)
        } else {
            bridgeImpl.handleError(activity, errorDetail)
        }
    }
}