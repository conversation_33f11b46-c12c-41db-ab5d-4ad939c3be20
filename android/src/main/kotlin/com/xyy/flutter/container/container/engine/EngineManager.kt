package com.xyy.flutter.container.container.engine

import android.content.Context
import android.util.Log
import com.xyy.flutter.container.container.ContainerRuntime
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.dart.DartExecutor
import java.lang.Exception

class EngineManager(engineManager: IEngineManager) : IEngineManager by engineManager {

    class EngineManagerDelegate(private val context: Context) : IEngineManager {

        private val engineFactory = EngineFactory()

        private val cachedEngineIds = mutableSetOf<String>()


        private val CACHE_ENGINE_COUNT = 3

        init {
            init()
        }

        //暂不预热engine
        override fun init() {
            loadNewEngineToCache()
        }

        /**
         * 获取一个新的闲置的引擎id，如果当前没有可用引擎，会新建一个引擎加入缓存
         * @return engine id,如果为null则表示engine超过最大数量或者创建失败
         */
        override fun getIdleEngineId(): String? {
            var firstIdleEngineId: String? = null
            var idleEngineCount = 0
            //判断当前缓存的引擎有没有闲置
            cachedEngineIds.forEach {
                val cachedEngine = FlutterEngineCache.getInstance().get(it)
                if (cachedEngine is XYYEngine && cachedEngine.isIdle()) {
                    idleEngineCount++
                    if (firstIdleEngineId.isNullOrEmpty()) {
                        firstIdleEngineId = it
                    }
                }
            }
            // 如果当前预热的引擎小于CACHE_ENGINE_COUNT，则循环新建引擎
            while (idleEngineCount < CACHE_ENGINE_COUNT) {
                // 加载新引擎
                val cacheNewEngine = createNewEngine()
                if (cacheNewEngine is XYYEngine) {
                    cacheNewEngine.dartExecutor.executeDartEntrypoint(
                            DartExecutor.DartEntrypoint.createDefault()
                    )
                }
                if (firstIdleEngineId.isNullOrEmpty()) {
                    firstIdleEngineId = (cacheNewEngine as XYYEngine).engineId
                }
                idleEngineCount++
            }
            return firstIdleEngineId
        }


        override fun createNewEngine(): FlutterEngine {
            val generateEngineId = generateEngineId()
            val initEngine = engineFactory.createEngine(context, generateEngineId)
            cachedEngineIds.add(generateEngineId)
            FlutterEngineCache.getInstance().put(generateEngineId, initEngine)
            return initEngine
        }


        override fun loadNewEngineToCache(): String? {
            return getIdleEngineId()

        }

        override fun isMaximumEngineCount(): Boolean {
            return cachedEngineIds.size >= ContainerRuntime.config.cacheEngineCount
        }

        override fun removeEngine(engineId: String?) {
            try {
                cachedEngineIds.remove(engineId)
            } catch (e: Exception) {
                Log.e("guan", "clean engine error")
                e.printStackTrace()
            }
        }

        override fun getEngineCacheIds(): Set<String> {
            return cachedEngineIds
        }


        private fun isContainsEngineId(engineId: String): Boolean {
            val idsContains = cachedEngineIds.contains(engineId)
            val cacheContains = FlutterEngineCache.getInstance().contains(engineId)
            return idsContains || cacheContains
        }


        private fun generateEngineId(): String {
            return "engine-${System.currentTimeMillis()}"
        }

        override fun printEngineInfo() {
            cachedEngineIds.forEach {
                val cacheEngine = FlutterEngineCache.getInstance().get(it)
                Log.e("guan1", "engineId:${it}," +
                        "isIdle:${if (cacheEngine is XYYEngine) cacheEngine.isIdle() else "null"}," +
                        "engineHashCode:${cacheEngine.hashCode()}," +
                        "isDestroy:${cacheEngine?.plugins == null}")
            }
        }

    }
}
