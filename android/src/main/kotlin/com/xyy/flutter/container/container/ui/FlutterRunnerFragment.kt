package com.xyy.flutter.container.container.ui

import android.content.Context
import android.util.Log
import io.flutter.embedding.android.FlutterFragment
import io.flutter.embedding.engine.FlutterEngineCache

class FlutterRunnerFragment : FlutterFragment() {


    override fun onAttach(context: Context) {
        val cachedEngineId = super.getCachedEngineId()
        val cacheFlutterEngine = FlutterEngineCache.getInstance().get(cachedEngineId ?: "")
        if (cacheFlutterEngine == null) {
            arguments?.remove(ARG_CACHED_ENGINE_ID)
            arguments?.let {
                Log.e("guan1", "fragmentattach")
                it.remove(ARG_CACHED_ENGINE_ID)
                it.putString(ARG_INITIAL_ROUTE, initialRoute)
            }
        }
        super.onAttach(context)

    }


    override fun onStart() {
        Log.e("guan1","onstart ${cachedEngineId}")
        Log.e("guan1","onstart1 ${flutterEngine?.dartExecutor?.isExecutingDart}")
        super.onStart()
    }

    override fun getCachedEngineId(): String? {
        val cachedEngineId = super.getCachedEngineId()
        val cacheFlutterEngine = FlutterEngineCache.getInstance().get(cachedEngineId ?: "")
        Log.e("guan1", "getCachedEngineId1 ${cacheFlutterEngine == null}")
        return if (cacheFlutterEngine == null) {
            null
        } else {
            cachedEngineId
        }
    }


    override fun getInitialRoute(): String? {
        activity?.let {
            if (it is FlutterRunnerActivity) {
                Log.e("guan1", "initialroute ${it.intent.getStringExtra("route")}")
                return it.intent.getStringExtra("route")
            }
        }
        Log.e("guan1", "initialroute1 ${super.getInitialRoute()}")
        return super.getInitialRoute()
    }

}