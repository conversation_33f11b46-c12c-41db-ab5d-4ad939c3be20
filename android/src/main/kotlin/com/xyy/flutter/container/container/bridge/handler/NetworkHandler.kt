package com.xyy.flutter.container.container.bridge.handler

import android.app.Activity
import androidx.fragment.app.FragmentActivity
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.flutter.container.container.bridge.ContainerErrorCode
import com.xyy.flutter.container.container.bridge.callback.RequestCallback
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity

class NetworkHandler : BaseHandler() {

    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        val method = params["method"]?.toString() ?: "get"
        val path = params["path"]?.toString() ?: ""
        val contentType = params["content_type"]?.toString() ?: ""
        val requestParams = params["parameters"]
        val headerParams = params["header"]

        val headerMap = convertParamsToMap(headerParams)

        val bridgeImpl = ContainerRuntime.bridge.getBridgeImpl()
        if (bridgeImpl == null) {
            error(ContainerErrorCode.NOT_IMPLEMENTED.errorCode, ContainerErrorCode.NOT_IMPLEMENTED.errorMsg)
        } else {
            bridgeImpl.request(activity, method, path, contentType, requestParams, headerMap,
                    object : RequestCallback {

                        override fun success(responseStr: String?) {
                            <EMAIL>(responseStr)
                        }

                        override fun error(errorCode: String?, errorMessage: String?) {
                            <EMAIL>(errorCode ?: "", errorMessage)
                        }
                    })
        }
    }

    private fun convertParamsToMap(requestParams: Any?): Map<String, String> {
        if (requestParams is String) {
            try {
                val type = object : TypeToken<Map<String?, String?>?>() {}.type
                return Gson().fromJson(requestParams, type)
            } catch (e: Exception) {
            }
        } else if (requestParams is HashMap<*, *>) {
            return requestParams as Map<String, String>
        }
        return hashMapOf()
    }


}