package com.xyy.flutter.container.container

import com.xyy.flutter.container.container.ui.FlutterRunnerActivity

class ContainerConfig private constructor(builder: Builder) {

    val cacheEngineCount = builder.cacheEngineCount
    val containerClass = builder.containerClass
    var useDefaultCheckNativeInterceptor = builder.useDefaultCheckNativeInterceptor


    class Builder {

        internal var cacheEngineCount = 3
        internal var useDefaultCheckNativeInterceptor = true
        internal var containerClass: Class<out FlutterRunnerActivity> = FlutterRunnerActivity::class.java

        fun setCacheEngineCount(count: Int): Builder {
            cacheEngineCount = count
            return this
        }

        fun setUseDefaultCheckNativeInterceptor(use: Boolean): Builder {
            useDefaultCheckNativeInterceptor = use
            return this
        }

        fun setContainerClass(containerClass: Class<out FlutterRunnerActivity>): Builder {
            this.containerClass = containerClass
            return this
        }


        fun build(): ContainerConfig {
            return ContainerConfig(this)
        }

    }

}