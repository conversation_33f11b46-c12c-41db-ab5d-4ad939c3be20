package com.xyy.flutter.container.container.bridge.photo;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;

/**
 * Created by wh on 2018/6/11.
 */

public class CircleTextView extends View {
    private Paint mPaint;
    private int mTextSize;
    private int mTextColor;
    private int mBackground;
    private String mText;
    private int width, height;

    public CircleTextView(Context context) {
        this(context, null);
    }

    public CircleTextView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CircleTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mTextSize = sp2px(12);
        mBackground = Color.WHITE;
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setStyle(Paint.Style.FILL);
    }

    public void setColor(int bg, int color) {
        mBackground = bg;
        mTextColor = color;
        invalidate();
    }

    public void setText(String text) {
        mText = text;
        invalidate();
    }

    public void setTextSize(int sp) {
        mTextSize = sp2px(sp);
        invalidate();
    }


    public void setData(String text, int sp, int color) {
        mText = text;
        mTextSize = sp2px(sp);
        mTextColor = color;
        invalidate();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int wMode = MeasureSpec.getMode(widthMeasureSpec);
        int hMode = MeasureSpec.getMode(heightMeasureSpec);
        int wSize = MeasureSpec.getSize(widthMeasureSpec);
        int hSize = MeasureSpec.getSize(heightMeasureSpec);

        if (wMode == MeasureSpec.EXACTLY) {
            width = wSize;
        } else {
            width = 50;
        }

        if (hMode == MeasureSpec.EXACTLY) {
            height = hSize;
        } else {
            height = 50;
        }

        if (width != height) {
            width = height = 50;
        }

        setMeasuredDimension(width, height);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        mPaint.setColor(mBackground);
        mPaint.setTextSize(mTextSize);
        int min = Math.min(width, height);
        canvas.drawCircle(width / 2, height / 2, min / 2, mPaint);
        if (!TextUtils.isEmpty(mText)) {
            mPaint.setColor(mTextColor);
            float textWidth = mPaint.measureText(mText);
            float x = (width - textWidth) / 2;
            Paint.FontMetrics metrics = mPaint.getFontMetrics();
            float dy = -(metrics.descent + metrics.ascent) / 2;
            float y = height / 2 + dy;
            canvas.drawText(mText, x, y, mPaint);
        }
    }

    protected int sp2px(float sp) {
        final float scale = this.getResources().getDisplayMetrics().scaledDensity;
        return (int) (sp * scale + 0.5f);
    }
}
