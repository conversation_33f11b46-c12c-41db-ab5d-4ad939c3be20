package com.xyy.flutter.container.container.bridge

import android.app.Activity
import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.handler.*
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity
import io.flutter.embedding.android.FlutterFragment
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel

object MethodCallDispatcher {

    private val handlerMap: Map<String, Class<out BaseHandler>> = mapOf(
            "exit_container" to ExitContainerHandler::class.java,
            "storage" to StorageHandler::class.java,
            "router" to RouterHandler::class.java,
            "location" to LocationHandler::class.java,
            "network" to NetworkHandler::class.java,
            "toast" to ToastHandler::class.java,
            "select_photo" to PhotoHandler::class.java,
            "upload_photo" to UploadPhotoHandler::class.java,
            "report_error" to ReportErrorHandler::class.java
    )

    private val customHandlerMap: MutableMap<String, Class<out BaseHandler>?> = mutableMapOf()

    private val engineHandlerMap: MutableMap<Int, MethodChannel> = mutableMapOf()

    fun registerCustomHandler(name: String, handler: Class<out BaseHandler>?) {
        customHandlerMap[name] = handler
    }

    fun removeCustomHandler(name: String) {
        customHandlerMap.remove(name)
    }

    fun bind(activity: FragmentActivity?, flutterEngine: FlutterEngine) {
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "XYYXX_bridge").let {
            engineHandlerMap[flutterEngine.hashCode()] = it
            it.setMethodCallHandler { call, result ->
                dispatch(activity, call, result)
            }
        }
    }


    fun unBind(flutterEngine: FlutterEngine?) {
        flutterEngine?.dartExecutor?.binaryMessenger?.setMessageHandler("XYYXX_bridge", null)
        engineHandlerMap.remove(flutterEngine?.hashCode())
    }

    fun bridgeCall(name: String?, params: Any?, engine: FlutterEngine?) {
        if (!name.isNullOrEmpty()) {
            engineHandlerMap[engine.hashCode()]?.invokeMethod(name,params)
        }
    }

    private fun dispatch(activity: FragmentActivity?, call: MethodCall?, result: MethodChannel.Result?) {
        if (call == null || result == null) {
            return
        }

        if (activity == null) {
            result.error(
                    ContainerErrorCode.ACTIVITY_IS_NULL.errorCode,
                    ContainerErrorCode.ACTIVITY_IS_NULL.errorMsg,
                    null
            )
            return
        }

        var handlerClass: Class<out BaseHandler>? = null

        if (customHandlerMap.containsKey(call.method)) {
            handlerClass = customHandlerMap[call.method]
        } else if (handlerMap.containsKey(call.method)) {
            handlerClass = handlerMap[call.method]
        }


        if (handlerClass == null) {
            result.notImplemented()
            return
        } else {
            val newInstance = handlerClass.newInstance()
            newInstance.handleMethodCall(activity, call, result)
        }

    }


}