package com.xyy.flutter.container.container.bridge.handler

import android.app.Activity
import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity

class ExitContainerHandler : BaseHandler() {
    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        activity.finish()
    }
}