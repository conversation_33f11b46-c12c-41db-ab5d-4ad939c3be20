package com.xyy.flutter.container.container.bridge.handler

import android.annotation.SuppressLint
import android.app.Activity
import android.util.Log
import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.flutter.container.container.bridge.ContainerErrorCode
import com.xyy.flutter.container.container.bridge.callback.LocationCallback
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity

class LocationHandler : BaseHandler() {
    @SuppressLint("CheckResult", "MissingPermission")
    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        Log.e("guan", "调用了定位")
        val bridgeImpl = ContainerRuntime.bridge.getBridgeImpl()

        if (bridgeImpl == null) {
            error(ContainerErrorCode.NOT_IMPLEMENTED.errorCode, ContainerErrorCode.NOT_IMPLEMENTED.errorMsg)
        } else {
            bridgeImpl.locate(activity, object : LocationCallback {
                override fun result(isSuccess: Boolean, latitude: Double, longitude: Double, extras: Map<String, Any>?) {
                    if (isSuccess) {
                        val map = mutableMapOf<String, Any>(
                                "latitude" to latitude.toString(),
                                "longitude" to longitude.toString()
                        )
                        extras?.let {
                            map.putAll(it)
                        }
                        result?.success(map)
                    } else {
                        error("-2", "locate is fail")
                    }
                }
            })
        }
    }

}