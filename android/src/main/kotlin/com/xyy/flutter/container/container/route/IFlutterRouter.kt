package com.xyy.flutter.container.container.route

import android.content.Context

interface IFlutterRouter {

    fun open(context: Context?, url: String?, openCallback: IOpenCallback? = null)
    fun removeRouterInterceptor(interceptor: IRouterInterceptor)
    fun addRouterInterceptor(interceptor: IRouterInterceptor)
    fun addRouterInterceptor(interceptor: IRouterInterceptor, index: Int)
    fun openLastContainer(context: Context?, url: String?, openCallback: IOpenCallback? = null)
    fun openNewContainer(context: Context?, url: String?, openCallback: IOpenCallback?)
}