package com.xyy.flutter.container.container.route

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import android.util.Log
import com.xyy.flutter.container.container.ContainerConfig
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity

object PageStackManager : Application.ActivityLifecycleCallbacks {

    private val pageStack: LinkedHashMap<String, Activity> = linkedMapOf()

    var pendingAttachOpenCallback: IOpenCallback? = null


    fun initPageStack(context: Context?) {
        context?.applicationContext?.let {
            if (it is Application) {
                it.registerActivityLifecycleCallbacks(this)
            }
        }
    }

    fun isContainerTop(): Boolean {
        val topContainer = getTopContainer()
        return topContainer != null
    }

    fun getTopContainer(): FlutterRunnerActivity? {
        if (pageStack.isEmpty()) {
            return null
        }
        val activity = pageStack[pageStack.keys.last()]
        if (activity != null && activity is FlutterRunnerActivity) {
            return activity
        }
        return null
    }

    override fun onActivityPaused(activity: Activity) {
        //do nothing
    }

    override fun onActivityStarted(activity: Activity) {
        pageStack[activity.toString()] = activity

    }

    override fun onActivityDestroyed(activity: Activity) {
        pageStack.remove(activity.toString())
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
        //do nothing
    }

    override fun onActivityStopped(activity: Activity) {
        //do nothing
    }


    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        //do nothing
    }

    override fun onActivityResumed(activity: Activity) {
        //do nothing
    }

}