package com.xyy.flutter.container.container.bridge.handler

import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.flutter.container.container.bridge.ContainerErrorCode
import com.xyy.flutter.container.container.bridge.callback.UploadCallback
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity

class UploadPhotoHandler : BaseHandler() {
    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        val uploadUrl = params["uploadUrl"]?.toString() ?: ""
        val localPaths = (params["localPaths"] ?: emptyList<String>()) as List<String>
        val limitWidth = params["limitWidth"]?.toString()?.toIntOrNull() ?: 1024
        val limitHeight = params["limitHeight"]?.toString()?.toIntOrNull() ?: 1024
        val extraParams = params["extraParams"]?.let {
            if (it is Map<*, *>) {
                it.mapKeys { entry ->
                    entry.key.toString()
                }
            } else {
                null
            }
        } ?: emptyMap()
        val isUploadOrigin = extraParams["isUploadOrigin"]?.toString()?.toBoolean() ?: false
        val bridgeImpl = ContainerRuntime.bridge.getBridgeImpl()

        if (bridgeImpl == null) {
            error(ContainerErrorCode.NOT_IMPLEMENTED.errorCode, ContainerErrorCode.NOT_IMPLEMENTED.errorMsg)
        } else {
            bridgeImpl.uploadImage(activity, uploadUrl, localPaths, limitWidth, limitHeight,
                    object : UploadCallback {
                override fun success(urlList: List<String?>?) {
                    <EMAIL>?.success(urlList)
                }

                override fun error(errorCode: String?, errorMessage: String?) {
                    <EMAIL>(errorCode ?: "", errorMessage)
                }
            },extraParams, isUploadOrigin)
        }
    }
}