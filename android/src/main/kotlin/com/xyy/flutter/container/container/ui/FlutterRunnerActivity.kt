package com.xyy.flutter.container.container.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import com.google.gson.Gson
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.flutter.container.container.bridge.MethodCallDispatcher
import com.xyy.flutter.container.container.engine.XYYEngine
import com.xyy.flutter.container.container.route.IOpenCallback
import com.xyy.flutter.container.container.route.PageStackManager
import io.flutter.embedding.android.FlutterActivityLaunchConfigs
import io.flutter.embedding.android.FlutterFragment
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.android.TransparencyMode
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.FlutterShellArgs
import io.flutter.plugin.common.BasicMessageChannel
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.StringCodec

open class FlutterRunnerActivity : FlutterFragmentActivity() {


    var routerChannel: BasicMessageChannel<String>? = null
    var callbackChannel: MethodChannel? = null

    var pendingOpenCallback: IOpenCallback? = null

    var canFinish = false;


    init {
        loadPendingOpenCallback()
    }


    /**
     * 自定义FlutterFragment,主要用于解决FlutterFragment状态恢复后找不到cachedEngine的问题
     */
    override fun createFlutterFragment(): FlutterFragment {
        val backgroundMode = backgroundMode
        val renderMode = renderMode
        val transparencyMode = if (backgroundMode == FlutterActivityLaunchConfigs.BackgroundMode.opaque) TransparencyMode.opaque else TransparencyMode.transparent

        return if (cachedEngineId != null) {
            io.flutter.Log.v(
                    "FlutterFragmentActivity",
                    """
                        Creating FlutterFragment with cached engine:
                        Cached engine ID: $cachedEngineId
                        Will destroy engine when Activity is destroyed: ${shouldDestroyEngineWithHost()}
                        Background transparency mode: $backgroundMode
                        Will attach FlutterEngine to Activity: ${shouldAttachEngineToActivity()}
                        """.trimIndent())
            FlutterFragment.CachedEngineFragmentBuilder(FlutterRunnerFragment::class.java, cachedEngineId!!)
                    .renderMode(renderMode)
                    .transparencyMode(transparencyMode)
                    .handleDeeplinking(shouldHandleDeeplinking())
                    .shouldAttachEngineToActivity(shouldAttachEngineToActivity())
                    .destroyEngineWithFragment(shouldDestroyEngineWithHost())
                    .build()
        } else {
            io.flutter.Log.v(
                    "FlutterFragmentActivity",
                    """
                        Creating FlutterFragment with new engine:
                        Background transparency mode: $backgroundMode
                        Dart entrypoint: $dartEntrypointFunctionName
                        Initial route: $initialRoute
                        App bundle path: $appBundlePath
                        Will attach FlutterEngine to Activity: ${shouldAttachEngineToActivity()}
                        """.trimIndent())
            FlutterFragment.NewEngineFragmentBuilder(FlutterRunnerFragment::class.java)
                    .dartEntrypoint(dartEntrypointFunctionName)
                    .initialRoute(initialRoute)
                    .appBundlePath(appBundlePath)
                    .flutterShellArgs(FlutterShellArgs.fromIntent(intent))
                    .handleDeeplinking(shouldHandleDeeplinking())
                    .renderMode(renderMode)
                    .transparencyMode(transparencyMode)
                    .shouldAttachEngineToActivity(shouldAttachEngineToActivity())
                    .build()
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        Log.e("FlutterEngineCache", "onCreate before")
        checkAndFixIntentParams()
        super.onCreate(savedInstanceState)
        Log.e("FlutterEngineCache", "onCreate after")
    }

    override fun onResume() {
        Log.e("FlutterEngineCache", "onResume before")
        super.onResume()
        Log.e("FlutterEngineCache", "onResume after")
    }

    override fun onSaveInstanceState(outState: Bundle) {
        Log.e("FlutterEngineCache", "onSaveInstanceState before")
        super.onSaveInstanceState(outState)
        Log.e("FlutterEngineCache", "onSaveInstanceState after")
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        Log.e("FlutterEngineCache", "onRestoreInstanceState before")
        super.onRestoreInstanceState(savedInstanceState)
        Log.e("FlutterEngineCache", "onRestoreInstanceState after")
    }

    override fun onRestart() {
        Log.e("FlutterEngineCache", "onRestart before")
        super.onRestart()
        Log.e("FlutterEngineCache", "onRestart after")
    }

    override fun onPause() {
        Log.e("FlutterEngineCache", "onPause before")
        super.onPause()
        Log.e("FlutterEngineCache", "onPause after")
    }

    override fun onLowMemory() {
        Log.e("FlutterEngineCache", "onLowMemory before")
        super.onLowMemory()
        Log.e("FlutterEngineCache", "onLowMemory after")
    }

    override fun onTrimMemory(level: Int) {
        Log.e("FlutterEngineCache", "onTrimMemory before")
        super.onTrimMemory(level)
        Log.e("FlutterEngineCache", "onTrimMemory after")
    }

    private fun checkAndFixIntentParams() {
        Log.e("guan1", "onCreate,${cachedEngineId}")
        cachedEngineId?.let {
            if (it.isNotEmpty()) {
                val engine = FlutterEngineCache.getInstance().get(it)
                if (engine == null) {
                    // 传了engineId，但是engine为空。这种情况按新建引擎处理
                    intent.removeExtra("cached_engine_id")
                    intent.putExtra("route", intent.getStringExtra("uri_path") ?: "/")
                    intent.putExtra("xyy_reset_engine", true)
                    Log.e("guan1", "reset engine ${intent.getStringExtra("uri_path") ?: "/"}")
                }
            }
        }
    }


    fun loadPendingOpenCallback(openCallback: IOpenCallback? = null) {
        pendingOpenCallback = openCallback ?: PageStackManager.pendingAttachOpenCallback
        PageStackManager.pendingAttachOpenCallback = null
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        if (flutterEngine is XYYEngine) {
            flutterEngine.isAttachToActivity = true
            Log.e("guan", "unset engine idle ${flutterEngine.engineId},${flutterEngine.isIdle()}")
        }
        MethodCallDispatcher.bind(this, flutterEngine)
        initRouterChannel(flutterEngine)
        // 已经加载了引擎，引擎池预载新引擎
        ContainerRuntime.engineManager.loadNewEngineToCache()
        Log.e("guan1", "configureFlutterEngine ${flutterEngine.hashCode()}")
    }


    private fun initRouterChannel(flutterEngine: FlutterEngine) {
        if (routerChannel == null) {
            routerChannel = BasicMessageChannel<String>(flutterEngine.dartExecutor.binaryMessenger,
                    "router_channel",
                    StringCodec.INSTANCE).also {
                it.setMessageHandler { message, reply ->
                    if (!message.isNullOrEmpty()) {
                        ContainerRuntime.getFlutterRouter().open(this, message, object : IOpenCallback {
                            override fun result(resultData: Map<String, Any?>?) {
                                val toJson = Gson().toJson(resultData)
                                reply.reply(toJson)
                            }
                        })
                    }
                }
            }
            if (pendingOpenCallback != null) {
                callbackChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "callback_channel")
            }
        }
    }


    override fun finish() {
        if (canFinish || callbackChannel == null) {
            super.finish()
        }
        canFinish = true
        callbackChannel?.let { channel ->
            channel.invokeMethod("getResult", null, object : MethodChannel.Result {
                override fun notImplemented() {
                    finish()
                    Log.e("guan", "getResult 1")
                    pendingOpenCallback?.result(null)
                }

                override fun error(errorCode: String?, errorMessage: String?, errorDetails: Any?) {
                    finish()
                    Log.e("guan", "getResult 2")
                    pendingOpenCallback?.result(null)
                }

                override fun success(result: Any?) {
                    finish()
                    Log.e("guan", "getResult 3 ${result}")
                    if (result is Map<*, *>) {
                        val mapResult = result.mapKeys {
                            it.key?.toString() ?: ""
                        }.mapValues {
                            it.value ?: ""
                        }
                        pendingOpenCallback?.result(mapResult)
                    } else {
                        pendingOpenCallback?.result(null)
                    }
                }

            })
        }

    }

    override fun provideFlutterEngine(context: Context): FlutterEngine? {
//        val idleEngineId = ContainerRuntime.engineManager.getIdleEngineId();
//        val cacheEngine = FlutterEngineCache.getInstance().get(idleEngineId ?: "")
        Log.e("guan1", "provideFlutterEngine")
//        return cacheEngine ?: ContainerRuntime.engineManager.createNewEngine()
        return ContainerRuntime.engineManager.createNewEngine()
    }

    fun openUrl(url: String, openCallback: IOpenCallback?) {
        routerChannel?.send(url) { reply ->
            try {
                val fromJson = Gson().fromJson(reply, Map::class.java)
                val mapResult = fromJson.mapKeys {
                    it.key?.toString() ?: ""
                }.mapValues {
                    it.value ?: ""
                }
                openCallback?.result(mapResult)
            } catch (e: Exception) {
                e.printStackTrace()
                Log.e("FlutterEngineCache", "openUrl callback error:${e}")
            }
            openCallback?.result(null)
        }
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        Log.e("FlutterEngineCache", "onActivityResult runner")
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10241) {
            val resultDataMap = HashMap<String, Any?>().also { map ->
                map["requestCode"] = requestCode
                map["resultCode"] = resultCode
                data?.extras?.let { bundle ->
                    bundle.keySet().forEach {
                        map[it] = bundle.get(it)
                    }
                }
            }
            pendingOpenCallback?.result(resultDataMap)
            pendingOpenCallback = null
        }
    }

    override fun onDestroy() {
        Log.e("guan1", "onDestroy ${flutterEngine?.hashCode() ?: "empty"}")
        flutterEngine?.let { engine ->
            if (engine is XYYEngine) {
                engine.isAttachToActivity = false
                Log.e("guan", "set engine idle ${engine.engineId},${engine.isIdle()}")
                ContainerRuntime.engineManager.removeEngine(engine.engineId)
            }
            engine.dartExecutor.binaryMessenger.let {
                //页面销毁时，需要注销这些已注册的channel，防止引擎携带activity的实例，从而导致内存泄漏
                it.setMessageHandler("router_channel", null)
                it.setMessageHandler("callback_channel", null)
                it.setMessageHandler("ybb_start_up_channel", null)
                MethodCallDispatcher.unBind(engine)
            }
        }
        Log.e("FlutterEngineCache", "onDestroy before")
        super.onDestroy()
        Log.e("FlutterEngineCache", "onDestroy after")

    }

}