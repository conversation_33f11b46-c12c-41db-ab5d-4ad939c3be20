<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#e1e1e5" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_photo"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#FFF4F5F9" />

    <LinearLayout
        android:id="@+id/ll_finish"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_gravity="bottom"
        android:background="#FFBDC1CE"
        android:gravity="center">

        <TextView
            android:id="@+id/tv_finish"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="完成"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

        <com.xyy.flutter.container.container.bridge.photo.CircleTextView
            android:id="@+id/tv_num"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginLeft="4dp"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>
