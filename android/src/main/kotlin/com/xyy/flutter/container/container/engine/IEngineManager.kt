package com.xyy.flutter.container.container.engine

import io.flutter.embedding.engine.FlutterEngine

interface IEngineManager {

    fun init()

    fun getIdleEngineId(): String?

    fun createNewEngine(): FlutterEngine

    fun loadNewEngineToCache():String?

    fun isMaximumEngineCount(): Boolean

    fun removeEngine(engineId: String?)

    fun getEngineCacheIds(): Set<String>

    fun printEngineInfo()

}