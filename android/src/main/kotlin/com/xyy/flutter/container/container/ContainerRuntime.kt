package com.xyy.flutter.container.container

import android.annotation.SuppressLint
import android.content.Context
import com.xyy.flutter.container.container.bridge.FlutterBridge
import com.xyy.flutter.container.container.bridge.IFlutterBridge
import com.xyy.flutter.container.container.engine.EngineManager
import com.xyy.flutter.container.container.engine.IEngineManager
import com.xyy.flutter.container.container.route.FlutterRouter
import com.xyy.flutter.container.container.route.IFlutterRouter
import com.xyy.flutter.container.container.route.PageStackManager

@SuppressLint("StaticFieldLeak")
object ContainerRuntime {

    lateinit var context: Context

    lateinit var config: ContainerConfig

    lateinit var router: IFlutterRouter

    lateinit var bridge: IFlutterBridge

    lateinit var engineManager: IEngineManager


    /**
     * 初始化
     */
    fun init(context: Context?, config: ContainerConfig? = null) {
        val appContext = context?.applicationContext ?: throw Exception("context is null")
        this.context = appContext
        this.config = config ?: ContainerConfig.Builder().build()

        initEngineManagement()
        initRouterManagement()
        initBridgeManagement()
    }

    private fun initBridgeManagement() {
        bridge = FlutterBridge(FlutterBridge.FlutterBridgeDelegate())
    }

    private fun initEngineManagement() {
        engineManager = EngineManager(EngineManager.EngineManagerDelegate(context))
    }

    private fun initRouterManagement() {
        router = FlutterRouter(FlutterRouter.FlutterRouterDelegate(context, engineManager))
    }


    /**
     * ---------------------------对外暴露方法-----------------------------
     */

    fun getFlutterBridge(): IFlutterBridge {
        return bridge
    }

    fun getFlutterRouter(): IFlutterRouter {
        return router
    }

}