package com.xyy.flutter.container.container.engine

import android.content.Context
import android.util.Log
import io.flutter.embedding.engine.FlutterEngine

class XYYEngine(context: Context, val engineId: String) : FlutterEngine(context) {
    var isAttachToActivity: Boolean = false


    fun isIdle(): Boolean {
        return !isAttachToActivity
    }

    init {
        Log.e("guan1", "engine ${hashCode()} init")
    }


    override fun destroy() {
        Log.e("guan1", "engine ${hashCode()} destroy")
        super.destroy()
    }

}