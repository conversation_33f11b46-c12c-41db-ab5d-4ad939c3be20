package com.xyy.flutter.container.container.bridge.photo;

import android.app.Activity;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.FileProvider;
import androidx.loader.app.LoaderManager;
import androidx.loader.content.CursorLoader;
import androidx.loader.content.Loader;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.xyy.flutter.container.container.R;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class AlbumActivity extends AppCompatActivity implements AlbumAdapter.OnItemClickListener {

    public static final int LOADER_ALL = 0;
    public static final int LOADER_OTHER = 1;
    public static final int REQUEST_CAMERA = 100;
    public static final String EXTRA_RESULT = "result";
    public static final String SOURCE_TYPE = "sourceType";//0：相册，1：相机
    public static final String SUFFIX_PHOTO = "xyy.png";
    public static final String MAX_PIECE = "max_piece";

    RecyclerView rvPhoto;
    LinearLayout llFinish;
    CircleTextView tvNum;

    private int mMaxPiece = 3;
    private int mColumn = 3;
    private AlbumAdapter mAdapter;
    private List<FolderBean> mFolderList = new ArrayList<>();
    private File mCameraFile;
    private List<PhotoBean> allPhoto;
    private static final String SELECTION = MediaStore.Files.FileColumns.MEDIA_TYPE + "=?"
            + " AND " + MediaStore.MediaColumns.SIZE + ">0";
    private static final String[] MEDIA_TYPE_IMAGE = new String[]{String.valueOf(MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE)};

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.flutter_activity_album);
        initView(savedInstanceState);
        initHead();
    }

    protected void initView(Bundle savedInstanceState) {
        rvPhoto = findViewById(R.id.rv_photo);
        llFinish = findViewById(R.id.ll_finish);
        tvNum = findViewById(R.id.tv_num);
        llFinish.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (v.getId() == R.id.ll_finish) {
                    if (mAdapter != null && mAdapter.getSelected() != null) {
                        Intent intent = new Intent();
                        intent.putParcelableArrayListExtra(EXTRA_RESULT, mAdapter.getSelected());
                        intent.putExtra(SOURCE_TYPE, 0);
                        setResult(RESULT_OK, intent);
                        finish();
                    }
                }
            }
        });
        mMaxPiece = getIntent().getIntExtra(MAX_PIECE, mMaxPiece);
        tvNum.setData("0", 14, 0xFF6377FE);
        GridLayoutManager manager = new GridLayoutManager(this, mColumn);
        rvPhoto.setLayoutManager(manager);
        mAdapter = new AlbumAdapter(this, mMaxPiece, false);
        mAdapter.setOnClickListener(this);
        rvPhoto.setAdapter(mAdapter);
        rvPhoto.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                outRect.left = 4;
                outRect.top = 4;
                outRect.right = 4;
                outRect.bottom = 4;
            }
        });
        LoaderManager loaderManager = getSupportLoaderManager();
        loaderManager.initLoader(LOADER_ALL, null, mLoaderCallback);
    }

    protected AbsNavigationBar initHead() {
        return new DefaultNavigationBar.Builder(this).setTitle("相册").setRightIcon(R.drawable.ic_take_photo).setRightClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openCamera();
            }
        }).builder();
    }

    //loader callback
    private LoaderManager.LoaderCallbacks<Cursor> mLoaderCallback = new LoaderManager.LoaderCallbacks<Cursor>() {
        String[] PROJECTION = new String[]{
                MediaStore.Images.Media._ID,//0
                MediaStore.Images.Media.DATA,//1
                MediaStore.Images.Media.DISPLAY_NAME,//2
                MediaStore.Images.Media.DATE_ADDED,//3
                MediaStore.Images.Media.MIME_TYPE,//4
                MediaStore.Images.Media.SIZE//5
        };

        @Override
        public Loader<Cursor> onCreateLoader(int id, Bundle args) {
            if (id == LOADER_ALL) {
                return new CursorLoader(AlbumActivity.this,
                        MediaStore.Files.getContentUri("external"),
                        PROJECTION,
                        SELECTION,
                        MEDIA_TYPE_IMAGE,
                        PROJECTION[3] + " desc");
            } else if (id == LOADER_OTHER) {
                String folder = args.getString("folder");
                return new CursorLoader(AlbumActivity.this,
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                        PROJECTION,
                        PROJECTION[1] + " like %" + folder + "% and " + PROJECTION[5] + ">0",
                        null,
                        PROJECTION[3] + " desc");
            }
            return null;
        }

        @Override
        public void onLoadFinished(Loader<Cursor> loader, Cursor data) {
            if (data != null) {
                if (allPhoto == null) {
                    allPhoto = new ArrayList<>(64);
                    PhotoBean item;
                    FolderBean folder;
                    File itemFile;
                    int nameIndex = data.getColumnIndex(PROJECTION[2]);
                    int pathIndex = data.getColumnIndex(PROJECTION[1]);
                    while (data.moveToNext()) {
                        item = new PhotoBean();
                        item.name = data.getString(nameIndex);
                        item.path = data.getString(pathIndex);
                        itemFile = new File(item.path);
                        String parent = getFolder(itemFile);
                        item.parent = parent;
                        allPhoto.add(item);
                        //把文件加入到相对应的folder中去
                        String folderName = TextUtils.isEmpty(parent) ? "" : itemFile.getParentFile().getName();
                        folder = findFolder(parent, item.path, folderName);
                        if (folder != null) {
                            folder.photos.add(item);
                        }
                    }
                    if (mAdapter != null) {
                        mAdapter.setData(allPhoto);
                    }
                }

            }
        }

        @Override
        public void onLoaderReset(Loader<Cursor> loader) {

        }
    };

    //通过一个文件，得到其所在的文件夹
    private String getFolder(File file) {
        if (file == null || !file.exists()) {
            return null;
        }
        return file.getParent();
    }

    //通过一个文件夹，得到一个文件夹对象
    public FolderBean findFolder(String folderPath, String cover, String name) {
        if (TextUtils.isEmpty(folderPath)) {
            return null;
        }
        FolderBean folderBean = null;
        for (int i = 0, len = mFolderList.size(); i < len; i++) {
            if (TextUtils.equals(folderPath, mFolderList.get(i).path)) {
                folderBean = mFolderList.get(i);
                break;
            }
        }
        if (folderBean == null) {
            FolderBean folder = new FolderBean();
            folder.path = folderPath;
            folder.cover = cover;
            folder.name = name;
            folder.photos = new ArrayList<>();
            mFolderList.add(folder);
        }
        return folderBean;
    }

    @Override
    public void onItemClick(int position, int count) {
        llFinish.setEnabled(count != 0);
        llFinish.setBackgroundColor(count != 0 ? Color.parseColor("#35C561") : Color.parseColor("#FFBDC1CE"));
        tvNum.setVisibility(count == 0 ? View.GONE : View.VISIBLE);
        tvNum.setText(String.valueOf(count));
        tvNum.setColor(Color.WHITE, Color.parseColor("#35C561"));
    }

    //打开相机
    private void openCamera() {
        String suffix = System.currentTimeMillis() + SUFFIX_PHOTO;
        mCameraFile = getFile(mCameraFile, suffix);
        Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        Uri imgUri;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                String authority = getPackageName() + ".fileprovider";
                imgUri = FileProvider.getUriForFile(this, authority, mCameraFile);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            } else {
                imgUri = Uri.fromFile(mCameraFile);
            }
            intent.putExtra(MediaStore.EXTRA_OUTPUT, imgUri);
        } catch (Exception e) {
            e.printStackTrace();
        }
        startActivityForResult(intent, REQUEST_CAMERA);
    }

    //获取一个目录
    private File getFile(File file, String photoName) {
        if (file == null) {
            String directory;
            if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())) {
                directory = Environment.getExternalStorageDirectory() + File.separator
                        + "DCIM" + File.separator + "camera" + File.separator;
            } else {
                directory = getCacheDir().getAbsolutePath() + File.separator + "pics" + File.separator;
            }
            file = new File(directory + photoName);
            if (!file.exists()) {
                file.getParentFile().mkdirs();
                try {
                    file.createNewFile();
                } catch (IOException e) {
                    e.printStackTrace();
                    return null;
                }
            }
        }
        if (file.exists()) {
            try {
                file.delete();
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        }
        return file;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == REQUEST_CAMERA) {
                File file = mCameraFile;
                if (data != null) {
                    String path = getCacheDir().getAbsolutePath() + "/tx_" + System.currentTimeMillis() + ".png";
                    Bundle bundle = data.getExtras();
                    if (bundle != null) {
                        Bitmap bitmap = (Bitmap) bundle.get("merchantBasicInfo");
                        file = BitmapUtil.bitmapToFile(bitmap, path, BitmapUtil.BIT_NOT_QUALITY);
                    } else {//android 5.0 系统
                        if (data.getData() != null && !TextUtils.isEmpty(data.getData().getEncodedPath())) {
                            if (BitmapUtil.compressFile(data.getData().getEncodedPath(), path)) {
                                file = new File(path);
                            }
                        }
                    }
                }
                ArrayList<PhotoBean> list = new ArrayList<>();
                if (file != null && file.exists()) {
                    PhotoBean bean = new PhotoBean();
                    bean.path = file.getAbsolutePath();
                    bean.parent = file.getParent();
                    bean.name = file.getName();
                    list.add(bean);
                }
                Intent intent = new Intent();
                intent.putParcelableArrayListExtra(EXTRA_RESULT, list);
                intent.putExtra(SOURCE_TYPE, 1);
                setResult(RESULT_OK, intent);
                finish();
            }
        } else {
            finish();
        }
    }
}
