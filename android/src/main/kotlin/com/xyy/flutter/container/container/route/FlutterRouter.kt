package com.xyy.flutter.container.container.route

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import android.widget.Toast
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.flutter.container.container.engine.IEngineManager
import com.xyy.flutter.container.container.engine.XYYEngine
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.BasicMessageChannel
import io.flutter.plugin.common.StringCodec

class FlutterRouter(flutterRouter: IFlutterRouter) : IFlutterRouter by flutterRouter {


    class FlutterRouterDelegate(
            context: Context?,
            private val engineManager: IEngineManager
    ) : IFlutterRouter {


        private val interceptorList = mutableListOf<IRouterInterceptor>()


        init {
            PageStackManager.initPageStack(context)
            if (ContainerRuntime.config.useDefaultCheckNativeInterceptor) {
                addRouterInterceptor(CheckNativeInterceptor())
            }
        }

        /**
         * 打开一个新的容器页面
         */
        override fun open(context: Context?, url: String?, openCallback: IOpenCallback?) {
            engineManager.printEngineInfo()
            PageStackManager.pendingAttachOpenCallback = null
            if (url.isNullOrEmpty()) {
                Toast.makeText(context, "router url is empty", Toast.LENGTH_SHORT).show()
                openCallback?.result(null)
                return
            }
            val topContainer = PageStackManager.getTopContainer()
            // 拦截器操作
            var transUrl = url
            interceptorList.forEach {
                transUrl = it.intercept(context, transUrl)
            }

            if (transUrl.isNullOrEmpty()) {
                openCallback?.let {
                    topContainer?.let {
                        it.loadPendingOpenCallback(openCallback)
                    }
                }
                return
            }

            //判断uri是否合法
            try {
                Uri.parse(transUrl)

                if (PageStackManager.isContainerTop()) {
                    openLastContainer(context, transUrl, openCallback)
                } else {
                    openNewContainer(context, transUrl, openCallback)
                }
            } catch (e: Exception) {
                Toast.makeText(context, "router url is empty", Toast.LENGTH_SHORT).show()
                openCallback?.result(null)
            }

        }

        override fun openNewContainer(context: Context?, url: String?, openCallback: IOpenCallback?) {
            Log.e("guan", "openNewContainer:${url}")
            context?.let {
                buildIntent(context, url)?.let { intent ->
                    context.startActivity(intent)
                    PageStackManager.pendingAttachOpenCallback = openCallback
                }
            }
        }

        override fun openLastContainer(context: Context?, url: String?, openCallback: IOpenCallback?) {
            Log.e("guan", "openLastContainer:${url}")
            val topContainer = PageStackManager.getTopContainer()
            topContainer?.let {
                topContainer.openUrl(url ?: "/", openCallback)
            }
        }


        override fun addRouterInterceptor(interceptor: IRouterInterceptor, index: Int) {
            interceptorList.add(index, interceptor)
        }

        override fun addRouterInterceptor(interceptor: IRouterInterceptor) {
            interceptorList.add(interceptor)
        }

        override fun removeRouterInterceptor(interceptor: IRouterInterceptor) {
            interceptorList.remove(interceptor)
        }

        /**
         * 构建新的容器Intent，用于打开容器页面
         */
        private fun buildIntent(context: Context, url: String?): Intent? {
            val intent: Intent?
            val idleEngineId = engineManager.getIdleEngineId()
            val cacheEngine = FlutterEngineCache.getInstance().get(idleEngineId ?: "")
            if (cacheEngine is XYYEngine) {
                cacheEngine.isAttachToActivity = true
                engineManager.loadNewEngineToCache()
            }
            Log.e("guan1", "idleEngineId:${idleEngineId},cacheEngine:${cacheEngine == null}")
            intent = if (idleEngineId.isNullOrEmpty()) {
                if (engineManager.isMaximumEngineCount()) {
                    Toast.makeText(context, "engine count has been the maximum", Toast.LENGTH_SHORT)
                            .show()
                    null
                } else {
                    //使用新engine
                    FlutterFragmentActivity
                            .NewEngineIntentBuilder(ContainerRuntime.config.containerClass)
                            .initialRoute(url ?: "/")
                            .build(context)
                }
            } else {
                //使用缓存的engine
                FlutterFragmentActivity
                        .CachedEngineIntentBuilder(ContainerRuntime.config.containerClass, idleEngineId)
                        .destroyEngineWithActivity(true)
                        .build(context).also {
                            preloadPage(url, idleEngineId)
                        }
            }
            return intent?.also {
                it.putExtra("uri_path", url)
//                it.putExtra("destroy_engine_with_activity", false)
                try {
                    val activity = context as Activity
                    //说明是页面
                } catch (e: Exception) {
                    //说明是ApplicationContext
                    it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
            }
        }

        private fun preloadPage(url: String?, idleEngineId: String) {
            //如果是复用引擎，提前跳转页面
            FlutterEngineCache.getInstance().get(idleEngineId)?.let {
                Log.e("guan1", "${idleEngineId} preloadPage:${url}")
                if (!url.isNullOrBlank()) {
                    BasicMessageChannel<String>(
                            it.dartExecutor.binaryMessenger,
                            "ybb_start_up_channel",
                            StringCodec.INSTANCE
                    ).send(url)
                }
            }
        }

    }


}