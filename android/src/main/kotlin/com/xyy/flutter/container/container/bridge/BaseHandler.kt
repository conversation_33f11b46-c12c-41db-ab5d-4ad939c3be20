package com.xyy.flutter.container.container.bridge

import android.app.Activity
import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity
import io.flutter.embedding.android.FlutterFragment
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel

abstract class BaseHandler {

    var result: MethodChannel.Result? = null

    abstract fun handle(activity: FragmentActivity, params: Map<String, Any?>)

    fun error(errorCode: String, errorMsg: String?) {
        result?.error(errorCode, errorMsg, null)
    }

    fun success(resultStr: String?) {
        result?.success(resultStr)
    }


    fun handleMethodCall(activity: FragmentActivity, call: MethodCall, result: MethodChannel.Result) {
        var arguments = call.arguments<Map<String, Any?>>()
        if (arguments == null) {
            arguments = mapOf<String, Any?>()
        }
        this.result = result
        try {
            handle(activity, arguments)
        } catch (e: Exception) {
            error(ContainerErrorCode.INTERNAL_ERROR.errorCode, e.message)
        }
    }


}
