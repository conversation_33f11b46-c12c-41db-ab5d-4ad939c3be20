package com.xyy.flutter.container.container.bridge.photo;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.xyy.flutter.container.container.R;


public class DefaultNavigationBar extends AbsNavigationBar<DefaultNavigationBar.Builder.DefaultNavigationBarParams> {
    public TextView titleTextView;
    public TextView rightTextView;
    public ImageView rightImgView;

    public DefaultNavigationBar(Builder.DefaultNavigationBarParams barParams) {
        super(barParams);
    }

    @Override
    public int bindHeadLayoutId() {
        return R.layout.flutter_defaulthead_layout;
    }

    @Override
    public void applyView() {
        //绑定效果
        titleTextView = setText(R.id.tv_toolbar_title, getParams().mTitle);
        rightTextView = setText(R.id.tv_rightText, getParams().mRightText);
        rightImgView = (ImageView) setIcon(R.id.iv_rightBt, getParams().mRightRes);
        setVisibility(R.id.default_unique_title_bar_line, getParams().mShowLine);
        rightTextView.setTextColor(getParams().mRightColor);
        setIcon(R.id.default_toolbar, getParams().mLeftRes);
        setOnClickListener(R.id.iv_rightBt, getParams().mRightClickListener);
        setOnClickListener(R.id.tv_rightText, getParams().mRightClickListener);
        setOnClickListener(R.id.default_toolbar, getParams().mLeftClickListener);
    }

    public static class Builder extends AbsNavigationBar.Builder {

        private DefaultNavigationBarParams p;

        public Builder(Context context, ViewGroup parent) {
            super(context, parent);
            p = new DefaultNavigationBarParams(context, parent);
        }

        public Builder(Context context) {
            super(context, null);
            p = new DefaultNavigationBarParams(context, null);
        }

        @Override
        public DefaultNavigationBar builder() {
            return new DefaultNavigationBar(p);
        }

        /**
         * 设置title
         *
         * @param title
         * @return
         */
        public Builder setTitle(String title) {
            p.mTitle = title;
            return this;
        }

        /**
         * 设置右边的文字
         *
         * @param rightText
         * @return
         */
        public Builder setRightText(String rightText) {
            p.mRightText = rightText;
            return this;
        }

        public Builder setRightTextColor(int color) {
            p.mRightColor = color;
            return this;
        }

        /**
         * 设置左边的图片
         *
         * @param leftRes
         * @return
         */
        public Builder setLeftIcon(int leftRes) {
            p.mLeftRes = leftRes;
            return this;
        }

        /**
         * 设置右边的图片
         *
         * @param rightRes
         * @return
         */
        public Builder setRightIcon(int rightRes) {
            p.mRightRes = rightRes;
            return this;
        }

        /**
         * 设置右边的点击事件
         *
         * @param rightClickListener
         * @return
         */
        public Builder setRightClickListener(View.OnClickListener rightClickListener) {
            p.mRightClickListener = rightClickListener;
            return this;
        }

        /**
         * 是否展示标题下面的线 默认展示
         *
         * @param showLine
         * @return
         */
        public Builder setShowLine(boolean showLine) {
            p.mShowLine = showLine;
            return this;
        }

        /**
         * 设置左边的点击事件
         *
         * @param leftClickListener
         * @return
         */
        public Builder setLeftClickListener(View.OnClickListener leftClickListener) {
            p.mLeftClickListener = leftClickListener;
            return this;
        }

        public static class DefaultNavigationBarParams extends AbsNavigationBarParams {
            //放所有效果
            private String mTitle;
            private String mRightText;
            private int mLeftRes;
            private int mRightRes;
            private int mRightColor;
            private boolean mShowLine = true;
            private View.OnClickListener mRightClickListener;
            private View.OnClickListener mLeftClickListener;

            public DefaultNavigationBarParams(final Context context, ViewGroup parent) {
                super(context, parent);
                mLeftClickListener = new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //默认关闭Activity
                        if (context instanceof Activity) {
                            ((Activity) context).finish();
                        }
                    }
                };
            }

        }
    }

}
